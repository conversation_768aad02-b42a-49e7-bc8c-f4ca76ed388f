import React, { useState, useMemo, useEffect, useRef, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../store/auth.store';
import { useFirmName } from '../hooks/useFirmName';
import { AppSidebar } from '../components/layout/AppSidebar';
import { SideBySideReview } from '../components/prepayments/SideBySideReview';
import { EditScheduleModal, type ScheduleEditData } from '../components/prepayments/EditScheduleModal';
import { api } from '@/lib/api';
import { PrepaymentsService, type PrepaymentsFilters } from '@/services/prepayments.service';
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from '../components/ui/breadcrumb';
import { Separator } from '../components/ui/separator';
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from '../components/ui/sidebar';

// --- Shadcn/UI & Lucide Imports ---
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import {
    Users,
    LogOut,
    Building,
    Briefcase,
    Menu,
    ChevronDown,
    FileText,
    AlertTriangle,
    CheckCircle2,
    Loader2,
    XCircle,
    Edit,
    X,
    Search,
    Filter,
    Eye,
    ChevronRight,
    Scissors,
    Ban,
    CheckCheck,
    CheckSquare,
    GripVertical,
} from 'lucide-react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

// --- Types ---
type UserInfo = { userId: string; displayName: string; email: string; firmName?: string; role: string; avatarUrl?: string; };
type Client = { clientId: string; clientName: string; };
type Entity = { entityId: string; entityName: string; type: string; connectionStatus: string; lastSync?: string; };

// Import types from service to ensure consistency
import type { SupplierData, InvoiceData, AmortizableLineItem, PaginationData } from '@/services/prepayments.service';

// --- Real API Functions ---
const fetchClients = async (): Promise<Client[]> => {
    try {
        const startTime = performance.now();
        const response = await api.getClients();
        const endTime = performance.now();
        console.log(`🚀 fetchClients: ${Math.round(endTime - startTime)}ms`);

        return response.clients.map(client => ({
            clientId: client.client_id,
            clientName: client.name // Now matches the updated interface
        }));
    } catch (error) {
        console.error('Error fetching clients:', error);
        throw new Error('Failed to fetch clients');
    }
};

const fetchEntities = async (clientId: string): Promise<Entity[]> => {
    try {
        const response = await api.getEntitiesForClient(clientId);
        console.log('DEBUG: Raw entities response:', response.entities);
        return response.entities.map(entity => ({
            entityId: entity.entity_id,
            entityName: entity.entity_name,
            type: entity.type || 'unknown',
            connectionStatus: entity.connection_status || 'unknown',
            lastSync: entity.last_sync
        }));
    } catch (error) {
        console.error('Error fetching entities:', error);
        throw new Error('Failed to fetch entities');
    }
};

interface DashboardFilters {
    dateRange?: string;
    status?: string;
    supplier?: string;
    showOnlyPending?: boolean;
    supplierFilter?: string;
    page?: number;
    pageSize?: number;
}

interface ScheduleDetails {
    id?: string;
    name: string;
    frequency: string;
    startDate: string;
    endDate?: string;
    amount: number;
    description?: string;
}

const fetchDashboardData = async (clientId: string, entityId: string | 'all', filters: DashboardFilters): Promise<{ suppliers: SupplierData[], pagination: PaginationData }> => {
    try {
        const prepaymentsFilters: PrepaymentsFilters = {
            client_id: clientId,
            entity_id: entityId !== 'all' ? entityId : undefined,
            page: filters.page || 1,
            limit: filters.pageSize || 10,
            supplier_filter: filters.supplierFilter,
            show_only_pending: filters.showOnlyPending,
        };

        const response = await PrepaymentsService.getPrepaymentsData(prepaymentsFilters);

        return {
            suppliers: response.suppliers,
            pagination: {
                currentPage: response.pagination.currentPage,
                pageSize: response.pagination.pageSize,
                totalItems: response.pagination.totalItems,
                totalPages: response.pagination.totalPages,
            }
        };
    } catch (error) {
        console.error('Error fetching dashboard data:', error);
        throw new Error('Failed to fetch dashboard data');
    }
};

// --- Real API Actions ---
const handleLineConfirm = async (scheduleId: string) => {
    try {
        await PrepaymentsService.confirmSchedule(scheduleId);
        // Refresh data after successful confirmation
        window.location.reload(); // TODO: Replace with proper state update
    } catch (error) {
        console.error('Error confirming schedule:', error);
        alert('Failed to confirm schedule. Please try again.');
    }
};

const handleLineSkip = async (scheduleId: string) => {
    const reason = prompt('Please provide a reason for skipping this schedule:');
    if (!reason) return;

    try {
        await PrepaymentsService.skipSchedule(scheduleId, reason);
        // Refresh data after successful skip
        window.location.reload(); // TODO: Replace with proper state update
    } catch (error) {
        console.error('Error skipping schedule:', error);
        alert('Failed to skip schedule. Please try again.');
    }
};

const handleCellEdit = (scheduleId: string, monthKey: string) => {
    // This will be handled by the EditScheduleModal
    alert(`Edit month ${monthKey} for schedule ${scheduleId} - Opening edit modal...`);
};

const handleLogout = () => { alert("Logout action triggered"); }
const getInitials = (name: string) => name.split(' ').map(n => n[0]).slice(0, 2).join('').toUpperCase();
const handleSupplierConfirmAll = (supplierId: string, entityId: string | 'all') => { alert(`Bulk Confirm triggered for Supplier ${supplierId} in Entity ${entityId}`); };
const handleInvoiceConfirmAll = (invoiceId: string) => { alert(`Bulk Confirm triggered for Invoice ${invoiceId}`); };
const handleInvoiceEdit = (invoiceId: string) => { alert(`Edit Invoice ${invoiceId} - (Action TBD: Edit first schedule? Open selection modal?)`); };
const handleInvoiceSkip = (invoiceId: string) => { alert(`Skip Invoice ${invoiceId} - (Action TBD: Skip all proposed schedules?)`); };

function PrepaymentsGrid({
    initialClientId = null, // Let it auto-select the first client
    currentUser = { userId: "user-admin-1", displayName: "Admin User", email: "<EMAIL>", firmName: "Example Firm", role: "firm_admin" }
 } : {
    initialClientId: string | null,
    currentUser: UserInfo;
 }) {
    const [clients, setClients] = useState<Client[]>([]);
    const [selectedClientId, setSelectedClientId] = useState<string | null>(initialClientId);
    const [entities, setEntities] = useState<Entity[]>([]);
    const [selectedEntityId, setSelectedEntityId] = useState<string | 'all'>('all');
    const [supplierFilter, setSupplierFilter] = useState<string>('');
    const [showOnlyPending, setShowOnlyPending] = useState<boolean>(true);
    const [dashboardSuppliers, setDashboardSuppliers] = useState<SupplierData[]>([]);
    const [pagination, setPagination] = useState<PaginationData>({ currentPage: 1, totalItems: 0, totalPages: 0 });
    const [isLoadingClients, setIsLoadingClients] = useState<boolean>(false);
    const [isLoadingEntities, setIsLoadingEntities] = useState<boolean>(false);
    const [isLoadingData, setIsLoadingData] = useState<boolean>(false);
    const [expandedSuppliers, setExpandedSuppliers] = useState<Record<string, boolean>>({});
    const [expandedInvoices, setExpandedInvoices] = useState<Record<string, boolean>>({});

    // SideBySideReview modal state
    const [reviewModalOpen, setReviewModalOpen] = useState<boolean>(false);
    const [selectedInvoiceForReview, setSelectedInvoiceForReview] = useState<InvoiceData | null>(null);
    const [reviewModalLoading, setReviewModalLoading] = useState<boolean>(false);
    const [reviewModalError, setReviewModalError] = useState<string | null>(null);

    // EditScheduleModal state
    const [editScheduleModalOpen, setEditScheduleModalOpen] = useState<boolean>(false);
    const [selectedScheduleForEdit, setSelectedScheduleForEdit] = useState<Omit<ScheduleEditData, 'originalAmount' | 'currencyCode'> | null>(null);
    const [selectedInvoiceForScheduleEdit, setSelectedInvoiceForScheduleEdit] = useState<InvoiceData | null>(null);
    const [editScheduleLoading, setEditScheduleLoading] = useState<boolean>(false);
    const [editScheduleError, setEditScheduleError] = useState<string | null>(null);
    const [availableAccounts, setAvailableAccounts] = useState<{ prepayment: any[], expense: any[] }>({ prepayment: [], expense: [] });

    const [monthColWidths, setMonthColWidths] = useState<Record<string, number>>({});
    const isResizing = useRef<number | null>(null);
    const startX = useRef<number>(0);
    const startWidth = useRef<number>(0);
    const tableRef = useRef<HTMLTableElement>(null);

    const MIN_COL_WIDTH = 120;
    const DEFAULT_FIRST_COL_WIDTH = 400;

    // Effects
    useEffect(() => {
        setIsLoadingClients(true);
        fetchClients()
        .then(fetchedClients => {
            console.log('DEBUG: Fetched clients:', fetchedClients);
            setClients(fetchedClients);
            if (!selectedClientId && fetchedClients.length > 0) {
                console.log('DEBUG: Setting selectedClientId to:', fetchedClients[0].clientId);
                setSelectedClientId(fetchedClients[0].clientId);
            }
        })
        .catch(error => {
            console.error('DEBUG: Error fetching clients:', error);
        })
        .finally(() => setIsLoadingClients(false));
    }, []);

    useEffect(() => {
        if (selectedClientId) {
            setIsLoadingEntities(true);
            setEntities([]);
            setDashboardSuppliers([]);
            setPagination({ currentPage: 1, totalItems: 0, totalPages: 0 });
            setExpandedSuppliers({});
            setExpandedInvoices({});
            fetchEntities(selectedClientId)
                .then(fetchedEntities => {
                    console.log('DEBUG: Fetched entities for client', selectedClientId, ':', fetchedEntities);
                    setEntities(fetchedEntities);
                })
                .catch(error => {
                    console.error('DEBUG: Error fetching entities:', error);
                })
                .finally(() => setIsLoadingEntities(false));
        } else {
            setEntities([]);
            setDashboardSuppliers([]);
            setPagination({ currentPage: 1, totalItems: 0, totalPages: 0 });
            setExpandedSuppliers({});
            setExpandedInvoices({});
        }
    }, [selectedClientId]);

    useEffect(() => {
        const currentPage = pagination.currentPage || 1;
        if (selectedClientId) {
            setIsLoadingData(true);
            const filters = {
                showOnlyPending: showOnlyPending,
                supplierFilter: supplierFilter || null,
                page: currentPage,
            }
            console.log('🚀 Starting dashboard data fetch...');
            const startTime = performance.now();

            fetchDashboardData(selectedClientId, selectedEntityId, filters)
                .then(data => {
                    const endTime = performance.now();
                    const duration = Math.round(endTime - startTime);
                    console.log(`✅ Dashboard data fetch completed: ${duration}ms`);

                    setDashboardSuppliers(data.suppliers);
                    setPagination(data.pagination);
                    if (filters.page === 1) {
                        setExpandedSuppliers({});
                        setExpandedInvoices({});
                    }
                 })
                .catch(error => {
                    console.error('DEBUG: fetchDashboardData error:', error);
                })
                .finally(() => setIsLoadingData(false));
        } else {
            setDashboardSuppliers([]);
            setPagination({ currentPage: 1, totalItems: 0, totalPages: 0 });
            setExpandedSuppliers({});
            setExpandedInvoices({});
        }
    }, [selectedClientId, selectedEntityId, supplierFilter, showOnlyPending, pagination.currentPage]);

    // Reset pagination when filters change (but not on initial load)
    const prevFiltersRef = useRef({ selectedEntityId, supplierFilter, showOnlyPending });
    useEffect(() => {
        const prev = prevFiltersRef.current;
        const current = { selectedEntityId, supplierFilter, showOnlyPending };

        // Only reset if filters actually changed (not on initial render)
        if (prev.selectedEntityId !== current.selectedEntityId ||
            prev.supplierFilter !== current.supplierFilter ||
            prev.showOnlyPending !== current.showOnlyPending) {
            setPagination(p => ({ ...p, currentPage: 1 }));
        }

        prevFiltersRef.current = current;
    }, [selectedEntityId, supplierFilter, showOnlyPending]);

    const monthColumns = useMemo(() => {
        const allMonthKeys = new Set<string>();
        dashboardSuppliers?.forEach(s => s.invoices?.forEach(i => i.amortizableLineItems?.forEach(l => Object.keys(l.monthlyBreakdown).forEach(k => allMonthKeys.add(k)))));

        const sortedKeys = Array.from(allMonthKeys).sort((a, b) => {
            const [yearA, monthA] = a.split('-').map(Number);
            const [yearB, monthB] = b.split('-').map(Number);
            if (yearA !== yearB) return yearA - yearB;
            return monthA - monthB;
        });

        setMonthColWidths(currentWidths => {
            const newWidths = { ...currentWidths };
            let changed = false;
            sortedKeys.forEach(key => {
                if (newWidths[key] === undefined) {
                    newWidths[key] = 150;
                    changed = true;
                }
            });
            Object.keys(newWidths).forEach(key => {
                if (!allMonthKeys.has(key)) {
                    delete newWidths[key];
                    changed = true;
                }
            });
            return changed ? newWidths : currentWidths;
        });

        if (sortedKeys.length === 0) {
             const months = []; const today = new Date();
             for (let i = 0; i < 6; i++) { const d=new Date(today.getFullYear(),today.getMonth()+i,1); months.push({ key: `${d.getFullYear()}-${(d.getMonth()+1).toString().padStart(2,'0')}`, label: d.toLocaleDateString('en-US',{month:'short',year:'numeric'})}); }
             return months;
        }

        return sortedKeys.map(k => {
            const [y,m]=k.split('-');
            const d=new Date(parseInt(y),parseInt(m)-1,1);
            return {key:k, label:d.toLocaleDateString('en-US',{month:'short',year:'numeric'})};
        });
    }, [dashboardSuppliers]);

    const handleMouseDown = useCallback((index: number, event: React.MouseEvent<HTMLDivElement>) => {
        isResizing.current = index;
        startX.current = event.clientX;

        const monthKey = monthColumns[index as number]?.key;
        if (monthKey) {
            startWidth.current = monthColWidths[monthKey] || MIN_COL_WIDTH;
        } else {
            isResizing.current = null;
            return;
        }

        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = 'col-resize';
        document.body.style.userSelect = 'none';

    }, [monthColumns, monthColWidths]);

    const handleMouseMove = useCallback((event: MouseEvent) => {
        if (isResizing.current === null) return;

        const currentX = event.clientX;
        const deltaX = currentX - startX.current;
        const newWidth = Math.max(MIN_COL_WIDTH, startWidth.current + deltaX);

        const monthKey = monthColumns[isResizing.current as number]?.key;
        if (monthKey) {
            setMonthColWidths(prevWidths => ({
                ...prevWidths,
                [monthKey]: newWidth,
            }));
        }

    }, [monthColumns]);

    const handleMouseUp = useCallback(() => {
        isResizing.current = null;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.body.style.cursor = '';
        document.body.style.userSelect = '';
    }, [handleMouseMove]);

    useEffect(() => {
        return () => {
            document.removeEventListener('mousemove', handleMouseMove);
            document.removeEventListener('mouseup', handleMouseUp);
        };
    }, [handleMouseMove, handleMouseUp]);

    const toggleSupplier = (id: string) => setExpandedSuppliers(p => ({...p, [id]: !p[id]}));
    const toggleInvoice = (id: string) => setExpandedInvoices(p => ({...p, [id]: !p[id]}));

    // Handle viewing attachment - opens SideBySideReview modal
    const handleViewAttachment = (attachmentId: string, invoice: InvoiceData) => {
        setSelectedInvoiceForReview(invoice);
        setReviewModalLoading(false);
        setReviewModalError(null);
        setReviewModalOpen(true);
    };

    // Fetch accounts for the selected entity
    const fetchAccountsForEntity = async (entityId: string) => {
        try {
            const response = await api.getXeroAccounts(entityId);
            const prepaymentAccounts = response.accounts.filter(acc => acc.type === 'ASSET');
            const expenseAccounts = response.accounts.filter(acc => acc.type === 'EXPENSE');
            setAvailableAccounts({ prepayment: prepaymentAccounts, expense: expenseAccounts });
        } catch (error) {
            console.error('Error fetching accounts:', error);
            // Fallback to default accounts
            setAvailableAccounts({
                prepayment: [
                    { code: '620', name: 'Prepaid Expenses' },
                    { code: '621', name: 'Prepaid Insurance' },
                    { code: '622', name: 'Prepaid Rent' }
                ],
                expense: [
                    { code: '5000', name: 'Office Expenses' },
                    { code: '5100', name: 'Insurance Expense' },
                    { code: '5200', name: 'Rent Expense' }
                ]
            });
        }
    };

    // Handle editing schedule - opens EditScheduleModal
    const handleEditSchedule = async (line: AmortizableLineItem, invoice: InvoiceData) => {
        setSelectedInvoiceForScheduleEdit(invoice);

        // Fetch accounts for the current entity if we have one
        if (selectedEntityId && selectedEntityId !== 'all') {
            await fetchAccountsForEntity(selectedEntityId);
        }

        // Convert AmortizableLineItem to the format expected by EditScheduleModal
        const scheduleData = {
            scheduleId: line.scheduleId,
            amortizationStartDate: '2025-04-01T00:00:00Z', // This would come from API
            amortizationEndDate: '2025-09-30T00:00:00Z', // This would come from API
            numberOfPeriods: Object.keys(line.monthlyBreakdown).length,
            prepaymentAccountCode: line.prepaymentAccountCode,
            expenseAccountCode: line.expenseAccountCode,
        };

        setSelectedScheduleForEdit(scheduleData);
        setEditScheduleModalOpen(true);
    };

    // Handle saving schedule changes
    const handleSaveSchedule = async (scheduleDetails: Omit<ScheduleEditData, 'scheduleId' | 'currencyCode'>, selectedLineIds: string[]) => {
        setEditScheduleLoading(true);
        setEditScheduleError(null);

        try {
            if (selectedScheduleForEdit?.scheduleId) {
                await PrepaymentsService.updateSchedule(selectedScheduleForEdit.scheduleId, {
                    amortizationStartDate: scheduleDetails.amortizationStartDate,
                    amortizationEndDate: scheduleDetails.amortizationEndDate,
                    numberOfPeriods: scheduleDetails.numberOfPeriods,
                    prepaymentAccountCode: scheduleDetails.prepaymentAccountCode,
                    expenseAccountCode: scheduleDetails.expenseAccountCode,
                });
            }
            setEditScheduleModalOpen(false);
            setSelectedScheduleForEdit(null);
            setSelectedInvoiceForScheduleEdit(null);
            // Refresh data after successful update
            window.location.reload(); // TODO: Replace with proper state update
        } catch (error) {
            console.error('Error saving schedule:', error);
            setEditScheduleError('Failed to save schedule');
        } finally {
            setEditScheduleLoading(false);
        }
    };

    const renderMonthlyCell = (line: AmortizableLineItem, monthKey: string) => {
        const monthData = line.monthlyBreakdown[monthKey];
        if (!monthData) return <span className="text-muted-foreground">-</span>;
        const isProposed = monthData.status === 'proposed';
        const isPosted = monthData.status === 'posted' || monthData.status === 'matched_manual';
        const isError = monthData.status === 'posting_error';
        const isSkipped = monthData.status === 'skipped';
        const isPosting = monthData.status === 'posting';

        const getStatusStyles = () => {
            switch (monthData.status) {
                case 'proposed': return { badgeVariant: 'outline', badgeClass: 'text-yellow-600 border-yellow-300 bg-white', icon: <AlertTriangle className="h-3 w-3 mr-1" />, cellBg: 'bg-yellow-50', cellBorder: 'border-yellow-300' };
                case 'posted':
                case 'matched_manual': return { badgeVariant: 'default', badgeClass: 'bg-green-100 text-green-700', icon: <CheckCheck className="h-3 w-3 mr-1" />, cellBg: 'bg-green-50', cellBorder: 'border-green-300' };
                case 'posting_error': return { badgeVariant: 'destructive', badgeClass: '', icon: <XCircle className="h-3 w-3 mr-1" />, cellBg: 'bg-red-50', cellBorder: 'border-red-300' };
                case 'skipped': return { badgeVariant: 'secondary', badgeClass: 'text-gray-500', icon: <Ban className="h-3 w-3 mr-1" />, cellBg: 'bg-gray-50', cellBorder: 'border-gray-300' };
                case 'posting': return { badgeVariant: 'secondary', badgeClass: 'text-blue-500', icon: <Loader2 className="h-3 w-3 mr-1 animate-spin" />, cellBg: 'bg-blue-50', cellBorder: 'border-blue-300' };
                default: return { badgeVariant: 'secondary', badgeClass: '', icon: null, cellBg: 'bg-white', cellBorder: 'border-transparent' };
            }
        };

        const { badgeVariant, badgeClass, icon, cellBg, cellBorder } = getStatusStyles();
        const tooltipContent = `Journal ID: ${monthData.journalId || 'N/A'}${monthData.error ? ` | Error: ${monthData.error}` : ''}`;
        const displayStatus = monthData.status.charAt(0).toUpperCase() + monthData.status.slice(1).replace('_', ' ');
        const buttonPlaceholderHeight = 'h-6';

        return (
             <TooltipProvider delayDuration={100}>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div className={`flex flex-col items-center justify-between p-1 border ${cellBorder} ${cellBg} rounded-md h-full min-h-[75px]`}>
                            <div className="flex flex-col items-center space-y-0.5 w-full">
                                <Badge variant={badgeVariant as "default" | "secondary" | "destructive" | "outline"} className={`${badgeClass} mb-0.5`}>
                                    {icon}
                                    {displayStatus}
                                </Badge>
                                <span className="font-medium text-sm">${monthData.amount.toFixed(2)}</span>
                                <span className="text-[10px] text-muted-foreground mt-0 truncate w-full px-1 text-center" title={`DR: ${line.expenseAccountCode || 'N/A'} / CR: ${line.prepaymentAccountCode}`}>
                                    DR:{line.expenseAccountCode || 'N/A'} / CR:{line.prepaymentAccountCode}
                                </span>
                            </div>

                            <div className={`flex justify-center items-center ${buttonPlaceholderHeight}`}>
                                {isProposed ? (
                                    <TooltipProvider delayDuration={100}>
                                        <Tooltip>
                                            <TooltipTrigger asChild>
                                                <Button variant="ghost" size="icon" className="h-5 w-5 text-blue-600 hover:bg-blue-100" onClick={() => handleCellEdit(line.scheduleId, monthKey)}><Edit className="h-4 w-4" /></Button>
                                            </TooltipTrigger>
                                            <TooltipContent><p>Edit Schedule</p></TooltipContent>
                                        </Tooltip>
                                    </TooltipProvider>
                                ) : (
                                    <div className={`${buttonPlaceholderHeight}`}></div>
                                )}
                            </div>

                            {isError && <span className="text-xs text-destructive text-center max-w-[100px] truncate absolute bottom-1 left-1/2 transform -translate-x-1/2">{monthData.error}</span>}
                        </div>
                    </TooltipTrigger>
                    {(monthData.journalId || monthData.error) && (
                        <TooltipContent><p>{tooltipContent}</p></TooltipContent>
                    )}
                </Tooltip>
            </TooltipProvider>
        );
    };

    return (
        <div className="space-y-4 h-full flex flex-col">
             <div className="flex flex-col md:flex-row gap-2 md:gap-4 items-center p-3 border-b bg-white flex-wrap flex-shrink-0 rounded-lg shadow-sm m-4 mb-0">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-full md:w-auto min-w-[200px] justify-between bg-white" disabled={isLoadingClients}>
                      {isLoadingClients ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                      {selectedClientId ? clients?.find(c => c.clientId === selectedClientId)?.clientName : 'Select Client'}
                      {!isLoadingClients && <ChevronDown className="ml-2 h-4 w-4" />}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-[300px] z-50">
                    <DropdownMenuLabel>Clients</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    {isLoadingClients && <DropdownMenuItem disabled>Loading...</DropdownMenuItem>}
                    {!isLoadingClients && clients && clients.map((client) => ( <DropdownMenuItem key={client.clientId} onSelect={() => setSelectedClientId(client.clientId)}>{client.clientName}</DropdownMenuItem> ))}
                    {!isLoadingClients && (!clients || clients.length === 0) && <DropdownMenuItem disabled>No clients found</DropdownMenuItem>}
                  </DropdownMenuContent>
                </DropdownMenu>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild disabled={!selectedClientId || isLoadingEntities}>
                    <Button variant="outline" className="w-full md:w-auto min-w-[200px] justify-between bg-white" disabled={!selectedClientId || isLoadingEntities}>
                       {isLoadingEntities ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : null}
                       {selectedEntityId === 'all' ? 'All Entities' : entities?.find(e => e.entityId === selectedEntityId)?.entityName ?? 'Select Entity'}
                       {!isLoadingEntities && <ChevronDown className="ml-2 h-4 w-4" />}
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="start" className="w-[300px] z-50">
                     <DropdownMenuLabel>Entities for {clients?.find(c => c.clientId === selectedClientId)?.clientName}</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                     <DropdownMenuItem key="all-entities" onSelect={() => setSelectedEntityId('all')}>All Entities</DropdownMenuItem>
                    {isLoadingEntities && <DropdownMenuItem disabled>Loading...</DropdownMenuItem>}
                    {!isLoadingEntities && entities && entities.map((entity) => ( <DropdownMenuItem key={entity.entityId} onSelect={() => setSelectedEntityId(entity.entityId)}>{entity.entityName} ({entity.connectionStatus})</DropdownMenuItem> ))}
                     {!isLoadingEntities && (!entities || entities.length === 0) && selectedClientId && <DropdownMenuItem disabled>No entities found</DropdownMenuItem>}
                     {!selectedClientId && <DropdownMenuItem disabled>Select a client first</DropdownMenuItem>}
                  </DropdownMenuContent>
                </DropdownMenu>
                <div className="flex-grow"></div>
            </div>

             <div className="relative z-5 flex flex-col sm:flex-row gap-2 items-center p-3 border rounded-lg bg-white shadow-sm flex-shrink-0 mx-4">
                <div className="relative w-full sm:w-auto flex-grow sm:flex-grow-0">
                     <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                     <Input type="search" placeholder="Search suppliers..." value={supplierFilter} onChange={(e) => setSupplierFilter(e.target.value)} className="pl-8 sm:w-[300px] md:w-[300px] lg:w-[400px]" />
                </div>
                <div className="flex items-center space-x-2 sm:ml-auto pt-2 sm:pt-0 w-full sm:w-auto justify-end">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      // Load sample data for testing
                      const sampleSuppliers: SupplierData[] = [
                        {
                          supplierId: 'sample-aws',
                          supplierName: 'AWS (Sample)',
                          overallStatus: 'action_needed',
                          invoices: [
                            {
                              invoiceId: 'sample-inv-001',
                              reference: 'INV-001',
                              invoiceDate: '2025-05-01T00:00:00Z',
                              totalAmount: 1200,
                              currencyCode: 'USD',
                              hasAttachment: true,
                              attachmentId: 'sample-att-001',
                              ocrWarningMessage: null,
                              overallStatus: 'action_needed',
                              isPartialApplication: false,
                              amortizableLineItems: [
                                {
                                  lineItemId: 'sample-line-001',
                                  description: 'Annual Software License',
                                  lineAmount: 1200,
                                  scheduleId: 'sample-schedule-001',
                                  prepaymentAccountCode: '620',
                                  expenseAccountCode: '5000',
                                  overallStatus: 'proposed',
                                  monthlyBreakdown: {
                                    '2025-05': { status: 'proposed', amount: 200 },
                                    '2025-06': { status: 'proposed', amount: 200 },
                                    '2025-07': { status: 'proposed', amount: 200 },
                                    '2025-08': { status: 'proposed', amount: 200 },
                                    '2025-09': { status: 'proposed', amount: 200 },
                                    '2025-10': { status: 'proposed', amount: 200 }
                                  }
                                }
                              ]
                            }
                          ]
                        }
                      ];
                      setDashboardSuppliers(sampleSuppliers);
                      setPagination({ currentPage: 1, totalItems: 1, totalPages: 1 });
                    }}
                    className="mr-2"
                  >
                    Load Sample Data
                  </Button>
                  <label htmlFor="pending-only-toggle" className="text-sm font-medium whitespace-nowrap">Show only pending</label>
                  <Switch id="pending-only-toggle" checked={showOnlyPending} onCheckedChange={setShowOnlyPending} />
                </div>
            </div>

            <div className="border rounded-lg overflow-x-auto overflow-y-auto bg-white shadow-sm flex-grow mx-4 mb-4">
                <Table ref={tableRef} className="min-w-full border-collapse" style={{ tableLayout: 'fixed' }}>
                     <colgroup>
                        <col style={{ width: `${DEFAULT_FIRST_COL_WIDTH}px` }} />
                        {monthColumns.map((month, index) => (
                            <col key={month.key} style={{ width: `${monthColWidths[month.key] || MIN_COL_WIDTH}px` }} />
                        ))}
                    </colgroup>
                    <TableHeader className="sticky top-0 z-10 bg-gray-50">
                        <TableRow>
                            <TableHead className="sticky left-0 z-20 bg-gray-50 text-xs uppercase text-muted-foreground border-r">
                                Supplier / Invoice / Line Item
                            </TableHead>
                            {monthColumns && monthColumns.map((month, index) => (
                                <TableHead key={month.key} className="text-center min-w-[120px] text-xs uppercase text-muted-foreground relative group border-l">
                                    {month.label}
                                    <div
                                        onMouseDown={(e) => handleMouseDown(index, e)}
                                        className="absolute top-0 right-0 w-2 h-full cursor-col-resize bg-transparent group-hover:bg-blue-200 opacity-0 group-hover:opacity-50"
                                        title="Resize column"
                                    >
                                      <GripVertical className="h-4 w-4 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-gray-400" />
                                    </div>
                                </TableHead>
                            ))}
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoadingData && ( <TableRow><TableCell colSpan={1 + (monthColumns?.length || 0)} className="h-24 text-center"><Loader2 className="h-6 w-6 animate-spin inline mr-2"/> Loading data...</TableCell></TableRow> )}
                        {!isLoadingData && (!dashboardSuppliers || dashboardSuppliers.length === 0) && ( <TableRow><TableCell colSpan={1 + (monthColumns?.length || 0)} className="h-24 text-center">No transactions found matching criteria.</TableCell></TableRow> )}

                        {!isLoadingData && dashboardSuppliers && dashboardSuppliers.map((supplier) => (
                            <React.Fragment key={supplier.supplierId}>
                                <TableRow className="bg-gray-50 hover:bg-gray-100 border-t border-b">
                                    <TableCell className="sticky left-0 z-10 bg-gray-50 font-semibold cursor-pointer border-r p-3" onClick={() => toggleSupplier(supplier.supplierId)}>
                                        <div className="flex items-center justify-between">
                                            <div className="flex items-center">
                                                {expandedSuppliers[supplier.supplierId] ? <ChevronDown className="h-4 w-4 inline mr-2 opacity-50" /> : <ChevronRight className="h-4 w-4 inline mr-2 opacity-50" />}
                                                <span>{supplier.supplierName}</span>
                                                {supplier.overallStatus === 'action_needed' && ( <Badge variant="outline" className="ml-2 text-yellow-600 border-yellow-300 bg-yellow-50"><AlertTriangle className="h-3 w-3 mr-1" /> Action needed</Badge> )}
                                                {supplier.overallStatus === 'validation_error' && ( <Badge variant="destructive" className="ml-2"><AlertTriangle className="h-3 w-3 mr-1" /> Validation Error</Badge> )}
                                            </div>
                                             {supplier.overallStatus === 'action_needed' && supplier.invoices.some(inv => inv.overallStatus === 'action_needed') && (
                                                 <TooltipProvider>
                                                     <Tooltip>
                                                         <TooltipTrigger asChild>
                                                             <Button variant="ghost" size="icon" className="h-6 w-6 text-green-600 hover:bg-green-100" onClick={(e) => { e.stopPropagation(); handleSupplierConfirmAll(supplier.supplierId, selectedEntityId); }}>
                                                                 <CheckSquare className="h-4 w-4" />
                                                             </Button>
                                                         </TooltipTrigger>
                                                         <TooltipContent><p>Confirm all proposed for {supplier.supplierName}</p></TooltipContent>
                                                     </Tooltip>
                                                 </TooltipProvider>
                                             )}
                                        </div>
                                    </TableCell>
                                    {monthColumns.map(month => <TableCell key={month.key}></TableCell>)}
                                </TableRow>
                                {expandedSuppliers[supplier.supplierId] && supplier.invoices?.map((invoice) => {
                                    const amortizableLineCount = invoice.amortizableLineItems.filter(l => l.overallStatus !== 'excluded' && l.overallStatus !== 'validation_failed').length;
                                    const totalAmortizableAmount = invoice.amortizableLineItems.reduce((sum, line) => (line.overallStatus !== 'excluded' && line.overallStatus !== 'validation_failed' ? sum + line.lineAmount : sum), 0);

                                    return (
                                    <React.Fragment key={invoice.invoiceId}>
                                        <TableRow className="hover:bg-muted/50">
                                             <TableCell className="sticky left-0 z-10 bg-white pl-8 cursor-pointer border-r p-3" onClick={() => toggleInvoice(invoice.invoiceId)}>
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-2">
                                                        {expandedInvoices[invoice.invoiceId] ? <ChevronDown className="h-4 w-4 inline mr-1 opacity-50" /> : <ChevronRight className="h-4 w-4 inline mr-1 opacity-50" />}
                                                        <div className="flex flex-col">
                                                            <span>{invoice.reference}</span>
                                                            <span className="text-xs text-gray-500">{new Date(invoice.invoiceDate).toLocaleDateString()} - ${invoice.totalAmount.toFixed(2)} {invoice.currencyCode}</span>
                                                             {amortizableLineCount > 0 && (
                                                                <span className="text-xs text-muted-foreground mt-1">
                                                                    (Amortizing {amortizableLineCount} Line{amortizableLineCount !== 1 ? 's' : ''} / ${totalAmortizableAmount.toFixed(2)} {invoice.currencyCode})
                                                                </span>
                                                             )}
                                                        </div>
                                                         {invoice.overallStatus === 'fully_posted' && <Badge variant="default" className="ml-2 bg-green-100 text-green-700"><CheckCheck className="h-3 w-3 mr-1" /> Fully Posted</Badge>}
                                                         {invoice.overallStatus === 'validation_error' && <Badge variant="destructive" className="ml-2"><AlertTriangle className="h-3 w-3 mr-1" /> Validation Error</Badge>}
                                                         {invoice.overallStatus === 'skipped' && <Badge variant="secondary" className="ml-2 text-gray-500"><Ban className="h-3 w-3 mr-1" /> Skipped</Badge>}
                                                         {invoice.isPartialApplication && <TooltipProvider><Tooltip><TooltipTrigger asChild><Badge variant="secondary" className="ml-2"><Scissors className="h-3 w-3 mr-1" /> Partial</Badge></TooltipTrigger><TooltipContent><p>Only some lines from this invoice are being amortized.</p></TooltipContent></Tooltip></TooltipProvider>}
                                                         {invoice.hasAttachment && invoice.attachmentId && <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="ghost" size="icon" className="h-5 w-5" onClick={(e) => { e.stopPropagation(); handleViewAttachment(invoice.attachmentId!, invoice); }}><FileText className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>View Attachment</p></TooltipContent></Tooltip></TooltipProvider>}
                                                         {invoice.ocrWarningMessage && <TooltipProvider><Tooltip><TooltipTrigger asChild><AlertTriangle className="h-4 w-4 text-destructive" /></TooltipTrigger><TooltipContent><p>{invoice.ocrWarningMessage}</p></TooltipContent></Tooltip></TooltipProvider>}
                                                    </div>
                                                     <div className="flex items-center space-x-1">
                                                         {(invoice.overallStatus === 'action_needed' || invoice.overallStatus === 'partially_posted') && invoice.amortizableLineItems.some(l => l.overallStatus === 'proposed') && (
                                                            <>
                                                                <TooltipProvider>
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Button variant="ghost" size="icon" className="h-6 w-6 text-green-600 hover:bg-green-100" onClick={(e) => { e.stopPropagation(); handleInvoiceConfirmAll(invoice.invoiceId); }}>
                                                                                <CheckSquare className="h-4 w-4" />
                                                                            </Button>
                                                                        </TooltipTrigger>
                                                                        <TooltipContent><p>Confirm all proposed for invoice {invoice.reference}</p></TooltipContent>
                                                                    </Tooltip>
                                                                </TooltipProvider>
                                                                <TooltipProvider>
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Button variant="ghost" size="icon" className="h-6 w-6 text-blue-600 hover:bg-blue-100" onClick={(e) => { e.stopPropagation(); handleInvoiceEdit(invoice.invoiceId); }}>
                                                                                <Edit className="h-4 w-4" />
                                                                            </Button>
                                                                        </TooltipTrigger>
                                                                        <TooltipContent><p>Edit proposed schedules for invoice {invoice.reference}</p></TooltipContent>
                                                                    </Tooltip>
                                                                </TooltipProvider>
                                                                <TooltipProvider>
                                                                    <Tooltip>
                                                                        <TooltipTrigger asChild>
                                                                            <Button variant="ghost" size="icon" className="h-6 w-6 text-red-600 hover:bg-red-100" onClick={(e) => { e.stopPropagation(); handleInvoiceSkip(invoice.invoiceId); }}>
                                                                                <Ban className="h-4 w-4" />
                                                                            </Button>
                                                                        </TooltipTrigger>
                                                                        <TooltipContent><p>Skip all proposed schedules for invoice {invoice.reference}</p></TooltipContent>
                                                                    </Tooltip>
                                                                </TooltipProvider>
                                                            </>
                                                         )}
                                                     </div>
                                                </div>
                                             </TableCell>
                                             {monthColumns.map(month => <TableCell key={month.key}></TableCell>)}
                                        </TableRow>
                                         {expandedInvoices[invoice.invoiceId] && invoice.amortizableLineItems?.map((line) => (
                                             <TableRow key={line.lineItemId} className="hover:bg-muted/50">
                                                 <TableCell
                                                     className="sticky left-0 z-10 bg-white border-r p-3"
                                                     style={{ paddingLeft: '4rem' }}
                                                 >
                                                     <div className="flex flex-col">
                                                         <span className="text-sm">{line.description}</span>
                                                         <span className="text-xs text-gray-500">${line.lineAmount.toFixed(2)}</span>
                                                         {line.overallStatus === 'excluded' && <Badge variant="secondary" className="mt-1 w-fit">Excluded</Badge>}
                                                         {line.overallStatus === 'validation_failed' && <Badge variant="destructive" className="mt-1 w-fit">Validation Failed</Badge>}
                                                         {(line.overallStatus === 'proposed' || line.overallStatus === 'error_posting' || line.overallStatus === 'partially_posted') && (
                                                            <div className="flex space-x-1 mt-1">
                                                                {(line.overallStatus === 'proposed' || line.overallStatus === 'partially_posted') &&
                                                                     <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="ghost" size="icon" className="h-6 w-6 text-green-600 hover:bg-green-100" onClick={() => handleLineConfirm(line.scheduleId)}><CheckSquare className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>Confirm Remaining</p></TooltipContent></Tooltip></TooltipProvider>
                                                                }
                                                                <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="ghost" size="icon" className="h-6 w-6 text-orange-600 hover:bg-orange-100" onClick={() => handleEditSchedule(line, invoice)}><Edit className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>Edit Schedule</p></TooltipContent></Tooltip></TooltipProvider>
                                                                <TooltipProvider><Tooltip><TooltipTrigger asChild><Button variant="ghost" size="icon" className="h-6 w-6 text-blue-600 hover:bg-blue-100" onClick={() => handleLineSkip(line.scheduleId)}><Ban className="h-4 w-4" /></Button></TooltipTrigger><TooltipContent><p>Skip Remaining</p></TooltipContent></Tooltip></TooltipProvider>
                                                            </div>
                                                         )}
                                                     </div>
                                                 </TableCell>
                                                 {monthColumns && monthColumns.map(month => ( <TableCell key={month.key} className="text-center p-1 align-top min-w-[120px]">{renderMonthlyCell(line, month.key)}</TableCell>))}
                                             </TableRow>
                                         ))}
                                    </React.Fragment>
                                    );
                                })}
                            </React.Fragment>
                        ))}
                    </TableBody>
                </Table>
            </div>
             {!isLoadingData && pagination.totalPages > 1 && (
                 <div className="flex items-center justify-between py-6 mx-4 flex-shrink-0">
                    <div className="text-sm text-muted-foreground">
                        Page {pagination.currentPage} of {pagination.totalPages} ({pagination.totalItems} items)
                    </div>
                    <Pagination>
                        <PaginationContent>
                            <PaginationItem>
                                <PaginationPrevious
                                    onClick={() => setPagination(p => ({...p, currentPage: p.currentPage - 1}))}
                                    className={pagination.currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                                />
                            </PaginationItem>

                            {/* First page */}
                            {pagination.currentPage > 2 && (
                                <PaginationItem>
                                    <PaginationLink
                                        onClick={() => setPagination(p => ({...p, currentPage: 1}))}
                                        className="cursor-pointer"
                                    >
                                        1
                                    </PaginationLink>
                                </PaginationItem>
                            )}

                            {/* Ellipsis before current page */}
                            {pagination.currentPage > 3 && (
                                <PaginationItem>
                                    <PaginationEllipsis />
                                </PaginationItem>
                            )}

                            {/* Previous page */}
                            {pagination.currentPage > 1 && (
                                <PaginationItem>
                                    <PaginationLink
                                        onClick={() => setPagination(p => ({...p, currentPage: p.currentPage - 1}))}
                                        className="cursor-pointer"
                                    >
                                        {pagination.currentPage - 1}
                                    </PaginationLink>
                                </PaginationItem>
                            )}

                            {/* Current page */}
                            <PaginationItem>
                                <PaginationLink isActive className="cursor-default">
                                    {pagination.currentPage}
                                </PaginationLink>
                            </PaginationItem>

                            {/* Next page */}
                            {pagination.currentPage < pagination.totalPages && (
                                <PaginationItem>
                                    <PaginationLink
                                        onClick={() => setPagination(p => ({...p, currentPage: p.currentPage + 1}))}
                                        className="cursor-pointer"
                                    >
                                        {pagination.currentPage + 1}
                                    </PaginationLink>
                                </PaginationItem>
                            )}

                            {/* Ellipsis after current page */}
                            {pagination.currentPage < pagination.totalPages - 2 && (
                                <PaginationItem>
                                    <PaginationEllipsis />
                                </PaginationItem>
                            )}

                            {/* Last page */}
                            {pagination.currentPage < pagination.totalPages - 1 && (
                                <PaginationItem>
                                    <PaginationLink
                                        onClick={() => setPagination(p => ({...p, currentPage: pagination.totalPages}))}
                                        className="cursor-pointer"
                                    >
                                        {pagination.totalPages}
                                    </PaginationLink>
                                </PaginationItem>
                            )}

                            <PaginationItem>
                                <PaginationNext
                                    onClick={() => setPagination(p => ({...p, currentPage: p.currentPage + 1}))}
                                    className={pagination.currentPage >= pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                                />
                            </PaginationItem>
                        </PaginationContent>
                    </Pagination>
                 </div>
             )}

            {/* SideBySideReview Modal */}
            <SideBySideReview
                isOpen={reviewModalOpen}
                onClose={() => setReviewModalOpen(false)}
                transactionId={selectedInvoiceForReview?.invoiceId || null}
                transactionData={selectedInvoiceForReview ? {
                    transactionId: selectedInvoiceForReview.invoiceId,
                    reference: selectedInvoiceForReview.reference,
                    counterpartyName: dashboardSuppliers.find(s =>
                        s.invoices.some(inv => inv.invoiceId === selectedInvoiceForReview.invoiceId)
                    )?.supplierName || 'Unknown Supplier',
                    hasAttachment: selectedInvoiceForReview.hasAttachment,
                    attachmentId: selectedInvoiceForReview.attachmentId,
                    lineItems: selectedInvoiceForReview.amortizableLineItems.map(line => ({
                        lineItemId: line.lineItemId,
                        description: line.description,
                        lineAmount: line.lineAmount,
                        isAmortized: line.overallStatus !== 'excluded' && line.overallStatus !== 'validation_failed'
                    })),
                    currencyCode: selectedInvoiceForReview.currencyCode
                } : null}
                scheduleData={selectedInvoiceForReview?.amortizableLineItems.length > 0 ? {
                    status: selectedInvoiceForReview.overallStatus === 'action_needed' ? 'proposed' :
                           selectedInvoiceForReview.overallStatus === 'partially_posted' ? 'partially_posted' :
                           selectedInvoiceForReview.overallStatus === 'fully_posted' ? 'fully_posted' : 'proposed',
                    originalAmount: selectedInvoiceForReview.amortizableLineItems
                        .filter(line => line.overallStatus !== 'excluded')
                        .reduce((sum, line) => sum + line.lineAmount, 0),
                    amortizationStartDate: new Date().toISOString(),
                    amortizationEndDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000).toISOString(),
                    numberOfPeriods: 6,
                    amortizationAccountCode: selectedInvoiceForReview.amortizableLineItems[0]?.prepaymentAccountCode || '620',
                    expenseAccountCode: selectedInvoiceForReview.amortizableLineItems[0]?.expenseAccountCode || null
                } : null}
                attachmentUrl={selectedInvoiceForReview?.attachmentId ?
                    `/api/attachments/${selectedInvoiceForReview.attachmentId}` : null}
                isLoading={reviewModalLoading}
                error={reviewModalError}
            />

            {/* EditScheduleModal */}
            <EditScheduleModal
                isOpen={editScheduleModalOpen}
                onClose={() => setEditScheduleModalOpen(false)}
                initialScheduleData={selectedScheduleForEdit}
                allSourceLineItems={selectedInvoiceForScheduleEdit?.amortizableLineItems.map(line => ({
                    lineItemId: line.lineItemId,
                    description: line.description,
                    amount: line.lineAmount,
                    status: 'available' as const,
                    linkedScheduleId: line.scheduleId
                })) || []}
                currencyCode={selectedInvoiceForScheduleEdit?.currencyCode || 'USD'}
                availablePrepaymentAccounts={availableAccounts.prepayment}
                availableExpenseAccounts={availableAccounts.expense}
                onSave={handleSaveSchedule}
                isSaving={editScheduleLoading}
                saveError={editScheduleError}
            />
        </div>
    );
}

export function PrepaymentsPage() {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const { firmName, isLoading: firmNameLoading } = useFirmName();

  return (
    <SidebarProvider>
      <AppSidebar />
      <SidebarInset className="flex-1 overflow-hidden">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem className="hidden md:block">
                <BreadcrumbLink
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    navigate('/dashboard');
                  }}
                  className="cursor-pointer"
                >
                  {firmNameLoading ? 'Loading...' : firmName}
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator className="hidden md:block" />
              <BreadcrumbItem>
                <BreadcrumbPage>Prepayments</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </header>
        <div className="flex-1 overflow-auto">
          <PrepaymentsGrid
            initialClientId={null}
            currentUser={{
              userId: "user-admin-1",
              displayName: "Admin User",
              email: "<EMAIL>",
              firmName: "Example Firm",
              role: "firm_admin"
            }}
          />
      </div>
      </SidebarInset>
    </SidebarProvider>
  );
}
