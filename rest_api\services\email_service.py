import os
import logging
from typing import Optional, Dict, Any
from sendgrid import SendGrid<PERSON><PERSON>lient
from sendgrid.helpers.mail import Mail, To, From
from python_http_client.exceptions import HTTPError

logger = logging.getLogger(__name__)

class EmailService:
    """Service for sending emails using SendGrid"""
    
    def __init__(self):
        self.api_key = os.getenv("SENDGRID_API_KEY")
        self.from_email = os.getenv("SENDGRID_FROM_EMAIL", "<EMAIL>")
        self.from_name = os.getenv("SENDGRID_FROM_NAME", "DRCR Labs")
        self.template_id_password_reset = os.getenv("SENDGRID_TEMPLATE_ID_PASSWORD_RESET")
        
        if not self.api_key:
            logger.error("SENDGRID_API_KEY environment variable not set")
            raise ValueError("SendGrid API key is required")
        
        # Template ID is optional - we can send custom HTML emails if not set
        if not self.template_id_password_reset:
            logger.warning("SENDGRID_TEMPLATE_ID_PASSWORD_RESET not set - will use custom HTML for password reset emails")
        
        self.client = SendGridAPIClient(api_key=self.api_key)
    
    async def send_password_reset_email(
        self, 
        to_email: str, 
        user_name: str, 
        reset_link: str,
        expiry_time: str = "24 hours"
    ) -> bool:
        """
        Send password reset email using SendGrid template or custom HTML
        
        Args:
            to_email: Recipient email address
            user_name: User's display name or email
            reset_link: Complete password reset URL
            expiry_time: Token expiration time description
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            if self.template_id_password_reset:
                # Use SendGrid template
                return await self._send_template_email(to_email, user_name, reset_link, expiry_time)
            else:
                # Use custom HTML email
                return await self._send_custom_password_reset_email(to_email, user_name, reset_link, expiry_time)
                
        except Exception as e:
            logger.error(f"Unexpected error sending password reset email to {to_email}: {e}")
            return False
    
    async def _send_template_email(
        self, 
        to_email: str, 
        user_name: str, 
        reset_link: str, 
        expiry_time: str
    ) -> bool:
        """Send password reset email using SendGrid template"""
        try:
            # Prepare template data
            template_data = {
                "user_name": user_name,
                "reset_link": reset_link,
                "expiry_time": expiry_time,
                "company_name": "DRCR Labs",
                "support_email": "<EMAIL>"
            }
            
            # Create the email message
            message = Mail(
                from_email=From(self.from_email, self.from_name),
                to_emails=To(to_email)
            )
            
            # Set the template ID and dynamic data
            message.template_id = self.template_id_password_reset
            message.dynamic_template_data = template_data
            
            # Send the email
            response = self.client.send(message)
            
            # Check if the email was sent successfully
            if response.status_code in [200, 201, 202]:
                logger.info(f"Password reset email sent successfully to {to_email} using template")
                return True
            else:
                logger.error(f"Failed to send password reset email to {to_email}. Status: {response.status_code}")
                return False
                
        except HTTPError as e:
            logger.error(f"SendGrid HTTP error sending password reset email to {to_email}: {e}")
            return False
    
    async def _send_custom_password_reset_email(
        self, 
        to_email: str, 
        user_name: str, 
        reset_link: str, 
        expiry_time: str
    ) -> bool:
        """Send password reset email using custom HTML"""
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>Password Reset - DRCR Labs</title>
        </head>
        <body>
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
                <h2>Password Reset Request</h2>
                
                <p>Hello {user_name},</p>
                
                <p>We received a request to reset your password for your DRCR Labs account. If you made this request, click the button below to reset your password:</p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{reset_link}" style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Reset Password</a>
                </div>
                
                <p>This link will expire in {expiry_time}.</p>
                
                <p>If you didn't request a password reset, you can safely ignore this email. Your password will not be changed.</p>
                
                <p>If you're having trouble clicking the button, copy and paste the following URL into your browser:</p>
                <p style="word-break: break-all; color: #666;">{reset_link}</p>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                
                <p style="color: #666; font-size: 12px;">
                    This email was sent by DRCR Labs. If you have questions, contact <NAME_EMAIL>.
                </p>
            </div>
        </body>
        </html>
        """
        
        plain_text = f"""
        Password Reset Request
        
        Hello {user_name},
        
        We received a request to reset your password for your DRCR Labs account.
        
        Reset your password by visiting this link:
        {reset_link}
        
        This link will expire in {expiry_time}.
        
        If you didn't request a password reset, you can safely ignore this email.
        
        DRCR Labs
        """
        
        return await self.send_custom_email(
            to_email=to_email,
            subject="Password Reset - DRCR Labs",
            html_content=html_content,
            plain_text_content=plain_text
        )
    
    async def send_custom_email(
        self,
        to_email: str,
        subject: str,
        html_content: str,
        plain_text_content: Optional[str] = None
    ) -> bool:
        """
        Send a custom email (not using templates)
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            html_content: HTML email content
            plain_text_content: Plain text email content (optional)
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        try:
            message = Mail(
                from_email=From(self.from_email, self.from_name),
                to_emails=To(to_email),
                subject=subject,
                html_content=html_content,
                plain_text_content=plain_text_content
            )
            
            response = self.client.send(message)
            
            if response.status_code in [200, 201, 202]:
                logger.info(f"Custom email sent successfully to {to_email}")
                return True
            else:
                logger.error(f"Failed to send custom email to {to_email}. Status: {response.status_code}")
                return False
                
        except HTTPError as e:
            logger.error(f"SendGrid HTTP error sending custom email to {to_email}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error sending custom email to {to_email}: {e}")
            return False

    async def send_invitation_email(
        self, 
        to_email: str, 
        user_name: str, 
        inviter_name: str,
        firm_name: str,
        setup_link: str,
        expiry_time: str = "24 hours"
    ) -> bool:
        """
        Send user invitation email with custom HTML
        
        Args:
            to_email: Recipient email address
            user_name: User's display name or email
            inviter_name: Name of the person who sent the invitation
            firm_name: Name of the firm they're being invited to
            setup_link: Complete account setup URL
            expiry_time: Token expiration time description
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>You're Invited to Join {firm_name} - DRCR Labs</title>
        </head>
        <body>
            <div style="max-width: 600px; margin: 0 auto; font-family: Arial, sans-serif;">
                <h2>You're Invited to Join {firm_name}</h2>
                
                <p>Hello {user_name},</p>
                
                <p>{inviter_name} has invited you to join <strong>{firm_name}</strong> on DRCR Labs.</p>
                
                <p>To get started, click the button below to set up your account:</p>
                
                <div style="text-align: center; margin: 30px 0;">
                    <a href="{setup_link}" style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">Set Up My Account</a>
                </div>
                
                <p>This invitation will expire in {expiry_time}.</p>
                
                <p>If you're having trouble clicking the button, copy and paste the following URL into your browser:</p>
                <p style="word-break: break-all; color: #666;">{setup_link}</p>
                
                <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
                
                <p style="color: #666; font-size: 12px;">
                    This invitation was sent by {inviter_name} from {firm_name}. If you have questions, contact them directly or reach out to <NAME_EMAIL>.
                </p>
            </div>
        </body>
        </html>
        """
        
        plain_text = f"""
        You're Invited to Join {firm_name}
        
        Hello {user_name},
        
        {inviter_name} has invited you to join {firm_name} on DRCR Labs.
        
        Set up your account by visiting this link:
        {setup_link}
        
        This invitation will expire in {expiry_time}.
        
        If you have questions, contact {inviter_name} directly or reach out to <NAME_EMAIL>.
        
        DRCR Labs
        """
        
        return await self.send_custom_email(
            to_email=to_email,
            subject=f"You're Invited to Join {firm_name} - DRCR Labs",
            html_content=html_content,
            plain_text_content=plain_text
        )

# Helper function to get email service instance
def get_email_service() -> EmailService:
    """Get an EmailService instance (creates new instance each time)"""
    return EmailService() 