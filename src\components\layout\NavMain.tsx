"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import { useNavigate, useLocation } from "react-router-dom"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "../ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "../ui/sidebar"

export function NavMain({
  items,
}: {
  items: {
    title: string
    url: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
      disabled?: boolean
    }[]
  }[]
}) {
  const navigate = useNavigate()
  const location = useLocation()

  return (
    <SidebarGroup>
      <SidebarMenu className="space-y-1">
        {items.map((item) => (
          <Collapsible
            key={item.title}
            asChild
            defaultOpen={item.isActive || location.pathname.startsWith(item.url)}
            className="group/collapsible"
          >
            <SidebarMenuItem>
              <CollapsibleTrigger asChild>
                <SidebarMenuButton 
                  tooltip={item.title}
                  className="h-9 px-3 font-medium hover:bg-sidebar-accent data-[state=open]:bg-sidebar-accent/50"
                >
                  {item.icon && <item.icon className="h-4 w-4" />}
                  <span className="text-sm">{item.title}</span>
                  <ChevronRight className="ml-auto h-4 w-4 transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                </SidebarMenuButton>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <SidebarMenuSub className="ml-6 mt-1 space-y-1">
                  {item.items?.map((subItem) => (
                    <SidebarMenuSubItem key={subItem.title}>
                      <SidebarMenuSubButton
                        asChild={!subItem.disabled}
                        className={`h-8 px-3 text-sm font-normal ${
                          subItem.disabled
                            ? 'text-muted-foreground cursor-not-allowed opacity-50'
                            : 'hover:bg-sidebar-accent cursor-pointer'
                        }`}
                      >
                        {subItem.disabled ? (
                          <span className="flex items-center">
                            <span>{subItem.title}</span>
                            <span className="ml-auto text-xs text-muted-foreground">(Coming Soon)</span>
                          </span>
                        ) : (
                          <a href={subItem.url} onClick={(e) => {
                            e.preventDefault()
                            navigate(subItem.url)
                          }}>
                            <span>{subItem.title}</span>
                          </a>
                        )}
                      </SidebarMenuSubButton>
                    </SidebarMenuSubItem>
                  ))}
                </SidebarMenuSub>
              </CollapsibleContent>
            </SidebarMenuItem>
          </Collapsible>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}
