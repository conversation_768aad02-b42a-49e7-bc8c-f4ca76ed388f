import { api } from '../lib/api';

export interface FirmDetails {
  firm_id: string;
  name: string;
  status: string;
  subscription_tier?: string;
  contact_email?: string;
  contact_phone?: string;
  address?: any;
  settings?: any;
}

export class FirmService {
  /**
   * Get firm details by firm ID
   * Note: This would require a backend endpoint to be implemented
   * For now, we'll return a fallback
   */
  static async getFirmDetails(firmId: string): Promise<FirmDetails | null> {
    try {
      // TODO: Implement actual API call when backend endpoint is available
      // const response = await api.get<FirmDetails>(`/firms/${firmId}`);
      // return response;
      
      // For now, return null to trigger fallback behavior
      return null;
    } catch (error) {
      console.error('Error fetching firm details:', error);
      return null;
    }
  }

  /**
   * Get firm name with fallback logic
   */
  static async getFirmName(firmId: string | undefined): Promise<string> {
    if (!firmId) {
      return 'DRCR'; // Fallback to app name
    }

    try {
      const firmDetails = await this.getFirmDetails(firmId);
      if (firmDetails?.name) {
        return firmDetails.name;
      }
    } catch (error) {
      console.error('Error getting firm name:', error);
    }

    // Fallback to a formatted firm ID or generic name
    return `Firm ${firmId.slice(-8)}`; // Show last 8 characters of firm ID
  }
}
