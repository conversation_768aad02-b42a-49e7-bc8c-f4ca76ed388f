import * as React from "react"
import { useNavigate, useLocation } from "react-router-dom"
import {
  Calculator,
  Settings,
  LogOut,
} from "lucide-react"

import { useAuthStore } from "../../store/auth.store"
import { NavMain } from "./NavMain"
import { NavUser } from "./NavUser"
import { toast } from "sonner"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "../ui/sidebar"

// Simplified navigation structure for DRCR
const navMainItems = [
  {
    title: "Prepayments",
    url: "/prepayments",
    icon: Calculator,
    items: [
      {
        title: "View All",
        url: "/prepayments",
      },
      {
        title: "Add New",
        url: "/prepayments/new",
      },
      {
        title: "Import",
        url: "/prepayments/import",
      },
    ],
  },
  {
    title: "Settings",
    url: "/settings",
    icon: Settings,
    items: [
      {
        title: "General",
        url: "/settings/general",
      },
      {
        title: "Users",
        url: "/settings/users",
      },
      {
        title: "Integrations",
        url: "/settings/integrations",
      },
    ],
  },
]

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  const navigate = useNavigate()
  const location = useLocation()
  const { user, signOut } = useAuthStore()

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Logged out successfully')
      navigate('/login')
    } catch (error: any) {
      toast.error(error.message || 'Failed to log out')
    }
  }

  // Get user initials for avatar
  const getInitials = () => {
    if (user?.displayName) {
      return user.displayName
        .split(' ')
        .map(name => name[0])
        .join('')
        .toUpperCase()
    }
    return user?.email?.[0].toUpperCase() || 'U'
  }

  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader className="border-b border-sidebar-border p-4">
        {/* Logo - Standalone */}
        <div className="flex justify-center items-center">
          <img 
            src="/logo.png" 
            alt="DRCR Logo" 
            className="h-12 w-12 object-contain"
          />
        </div>
      </SidebarHeader>

      <SidebarContent className="px-3 py-4">
        {/* Dashboard - Moved here */}
        <SidebarMenu className="mb-4">
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              tooltip="Dashboard"
              size="lg"
              className="h-10 px-3 font-medium hover:bg-sidebar-accent"
            >
              <a href="/dashboard" onClick={(e) => {
                e.preventDefault()
                navigate("/dashboard")
              }}>
                <span className="font-semibold text-base">Dashboard</span>
              </a>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>

        {/* Section Label */}
        <div className="mb-3">
          <h4 className="text-xs font-medium text-sidebar-foreground/60 uppercase tracking-wider px-3">
            Operations
          </h4>
        </div>
        
        {/* Main Navigation */}
        <NavMain items={navMainItems} />
      </SidebarContent>

      <SidebarFooter className="border-t border-sidebar-border p-3">
        {/* User Profile */}
        <NavUser user={{
          name: user?.displayName || 'User',
          email: user?.email || '<EMAIL>',
          avatar: '', // No avatar for now
        }} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
