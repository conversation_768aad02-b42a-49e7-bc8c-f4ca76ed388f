import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'sonner';
import { Suspense, lazy } from 'react';
import { useParams } from 'react-router-dom';

import { AuthProvider } from './providers/AuthProvider';
import { ProtectedRoute } from './components/auth/ProtectedRoute';
import { LoadingSpinner } from './components/ui/loading-spinner';

// Lazy load components for better performance
const ShadcnLoginPage = lazy(() => import('./pages/ShadcnLoginPage'));
const ForgotPasswordPage = lazy(() => import('./pages/ForgotPasswordPage'));
const ResetPasswordPage = lazy(() => import('./pages/ResetPasswordPage'));
const DashboardPage = lazy(() => import('./pages/DashboardPage').then(module => ({ default: module.DashboardPage })));
const PrepaymentsPage = lazy(() => import('./pages/PrepaymentsPage').then(module => ({ default: module.PrepaymentsPage })));
const AccountPage = lazy(() => import('./pages/AccountPage').then(module => ({ default: module.AccountPage })));
const BillingPage = lazy(() => import('./pages/BillingPage').then(module => ({ default: module.BillingPage })));
const NotificationsPage = lazy(() => import('./pages/NotificationsPage').then(module => ({ default: module.NotificationsPage })));
const UserManagementPage = lazy(() => import('./pages/UserManagementPage'));
const XeroOrganizationSelector = lazy(() => import('./components/XeroOrganizationSelector').then(module => ({ default: module.XeroOrganizationSelector })));
const TailwindTest = lazy(() => import('./components/TailwindTest').then(module => ({ default: module.TailwindTest })));
const DraggableDialogDemo = lazy(() => import('./pages/DraggableDialogDemo'));
const EntityManagement = lazy(() => import('./pages/EntityManagement').then(module => ({ default: module.EntityManagement })));

// Component to handle Xero configure redirect
const XeroConfigureRedirect = () => {
  const { clientId } = useParams<{ clientId: string }>();
  return <Navigate to={`/clients/${clientId}/entities`} replace />;
};

function App() {
  return (
    <BrowserRouter>
      <AuthProvider>
        <Suspense fallback={<LoadingSpinner />}>
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<ShadcnLoginPage />} />
            <Route path="/forgot-password" element={<ForgotPasswordPage />} />
            <Route path="/reset-password" element={<ResetPasswordPage />} />
            <Route path="/tailwind-test" element={<TailwindTest />} />
            <Route path="/dialog-demo" element={<DraggableDialogDemo />} />

            {/* Protected routes */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <DashboardPage />
                </ProtectedRoute>
              }
            />

            <Route
              path="/prepayments"
              element={
                <ProtectedRoute>
                  <PrepaymentsPage />
                </ProtectedRoute>
              }
            />

            {/* User Profile Pages */}
            <Route
              path="/account"
              element={
                <ProtectedRoute>
                  <AccountPage />
                </ProtectedRoute>
              }
            />

            <Route
              path="/billing"
              element={
                <ProtectedRoute>
                  <BillingPage />
                </ProtectedRoute>
              }
            />

            <Route
              path="/notifications"
              element={
                <ProtectedRoute>
                  <NotificationsPage />
                </ProtectedRoute>
              }
            />

            {/* User Management */}
            <Route
              path="/settings/users"
              element={
                <ProtectedRoute requiredRole="firm_admin">
                  <UserManagementPage />
                </ProtectedRoute>
              }
            />

            {/* Xero Organization Selection */}
            <Route
              path="/clients/:clientId/xero/select-organization"
              element={
                <ProtectedRoute>
                  <XeroOrganizationSelector />
                </ProtectedRoute>
              }
            />

            {/* Entity Management */}
            <Route
              path="/clients/:clientId/entities"
              element={
                <ProtectedRoute>
                  <EntityManagement />
                </ProtectedRoute>
              }
            />

            <Route
              path="/clients/:clientId/entities/:entityId"
              element={
                <ProtectedRoute>
                  <EntityManagement />
                </ProtectedRoute>
              }
            />

            {/* Xero Configure Redirect - handles OAuth callback redirects */}
            <Route
              path="/clients/:clientId/xero/configure"
              element={
                <ProtectedRoute>
                  <XeroConfigureRedirect />
                </ProtectedRoute>
              }
            />

            {/* Redirect root to dashboard */}
            <Route path="/" element={<Navigate to="/dashboard" replace />} />

            {/* Catch all - redirect to dashboard */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Suspense>

        <Toaster position="top-right" />
      </AuthProvider>
    </BrowserRouter>
  );
}

export default App;
