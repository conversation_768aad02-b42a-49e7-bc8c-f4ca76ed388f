import React from 'react';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import type { DashboardState } from '../types';

interface DashboardPaginationProps {
  pagination: DashboardState['pagination'];
  isLoading: boolean;
  onPageChange: (page: number) => void;
}

export function DashboardPagination({ pagination, isLoading, onPageChange }: DashboardPaginationProps) {
  if (isLoading || !pagination || pagination.totalPages <= 1) {
    return null;
  }

  return (
    <div className="flex items-center justify-between py-6 flex-shrink-0">
      <div className="text-sm text-muted-foreground">
        Page {pagination.currentPage} of {pagination.totalPages} ({pagination.totalItems} items)
      </div>
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => onPageChange(pagination.currentPage - 1)}
              className={pagination.currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>

          {/* First page */}
          {pagination.currentPage > 2 && (
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(1)}
                className="cursor-pointer"
              >
                1
              </PaginationLink>
            </PaginationItem>
          )}

          {/* Ellipsis before current page */}
          {pagination.currentPage > 3 && (
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
          )}

          {/* Previous page */}
          {pagination.currentPage > 1 && (
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(pagination.currentPage - 1)}
                className="cursor-pointer"
              >
                {pagination.currentPage - 1}
              </PaginationLink>
            </PaginationItem>
          )}

          {/* Current page */}
          <PaginationItem>
            <PaginationLink isActive className="cursor-default">
              {pagination.currentPage}
            </PaginationLink>
          </PaginationItem>

          {/* Next page */}
          {pagination.currentPage < pagination.totalPages && (
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(pagination.currentPage + 1)}
                className="cursor-pointer"
              >
                {pagination.currentPage + 1}
              </PaginationLink>
            </PaginationItem>
          )}

          {/* Ellipsis after current page */}
          {pagination.currentPage < pagination.totalPages - 2 && (
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
          )}

          {/* Last page */}
          {pagination.currentPage < pagination.totalPages - 1 && (
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(pagination.totalPages)}
                className="cursor-pointer"
              >
                {pagination.totalPages}
              </PaginationLink>
            </PaginationItem>
          )}

          <PaginationItem>
            <PaginationNext
              onClick={() => onPageChange(pagination.currentPage + 1)}
              className={pagination.currentPage >= pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
} 