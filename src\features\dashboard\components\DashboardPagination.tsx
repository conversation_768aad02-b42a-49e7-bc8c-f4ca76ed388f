import React from 'react';
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';
import type { DashboardState } from '../types';

interface DashboardPaginationProps {
  pagination: DashboardState['pagination'];
  isLoading: boolean;
  onPageChange: (page: number) => void;
}

/**
 * Dashboard pagination component that only displays when there are multiple pages
 * Fixes the issue where pagination controls were shown even with single page of data
 * Handles both snake_case (backend) and camelCase (frontend) field naming conventions
 */

export function DashboardPagination({ pagination, isLoading, onPageChange }: DashboardPaginationProps) {
  // Handle different pagination field naming conventions
  const currentPage = pagination?.current_page || pagination?.currentPage || 1;
  const totalPages = pagination?.total_pages || pagination?.totalPages || 1;
  const totalItems = pagination?.total_items || pagination?.totalItems || 0;
  const pageSize = pagination?.page_size || pagination?.pageSize || 20;

  // Hide pagination controls when:
  // 1. Loading state
  // 2. No pagination data
  // 3. Only one page of data (totalPages <= 1)
  // 4. No items to display (totalItems <= 0)
  if (isLoading ||
      !pagination ||
      totalPages <= 1 ||
      totalItems <= 0) {
    return null;
  }

  return (
    <div className="flex items-center justify-between py-6 flex-shrink-0">
      <div className="text-sm text-muted-foreground">
        Page {currentPage} of {totalPages} ({totalItems} items)
      </div>
      <Pagination>
        <PaginationContent>
          <PaginationItem>
            <PaginationPrevious
              onClick={() => onPageChange(currentPage - 1)}
              className={currentPage <= 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>

          {/* First page */}
          {currentPage > 2 && (
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(1)}
                className="cursor-pointer"
              >
                1
              </PaginationLink>
            </PaginationItem>
          )}

          {/* Ellipsis before current page */}
          {currentPage > 3 && (
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
          )}

          {/* Previous page */}
          {currentPage > 1 && (
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(currentPage - 1)}
                className="cursor-pointer"
              >
                {currentPage - 1}
              </PaginationLink>
            </PaginationItem>
          )}

          {/* Current page */}
          <PaginationItem>
            <PaginationLink isActive className="cursor-default">
              {currentPage}
            </PaginationLink>
          </PaginationItem>

          {/* Next page */}
          {currentPage < totalPages && (
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(currentPage + 1)}
                className="cursor-pointer"
              >
                {currentPage + 1}
              </PaginationLink>
            </PaginationItem>
          )}

          {/* Ellipsis after current page */}
          {currentPage < totalPages - 2 && (
            <PaginationItem>
              <PaginationEllipsis />
            </PaginationItem>
          )}

          {/* Last page */}
          {currentPage < totalPages - 1 && (
            <PaginationItem>
              <PaginationLink
                onClick={() => onPageChange(totalPages)}
                className="cursor-pointer"
              >
                {totalPages}
              </PaginationLink>
            </PaginationItem>
          )}

          <PaginationItem>
            <PaginationNext
              onClick={() => onPageChange(currentPage + 1)}
              className={currentPage >= totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
            />
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </div>
  );
}