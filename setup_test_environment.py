#!/usr/bin/env python3
"""
Setup script for testing the automated sync system locally.
Prepares the environment and starts necessary services.
"""

import os
import sys
import subprocess
import time
import requests
from datetime import datetime

def print_header(title: str):
    """Print a formatted header."""
    print("\n" + "=" * 60)
    print(f" {title}")
    print("=" * 60)

def print_step(step: str):
    """Print a formatted step."""
    print(f"\n🔧 {step}")
    print("-" * 40)

def run_command(command: str, cwd: str = None, background: bool = False):
    """Run a shell command."""
    print(f"Running: {command}")
    if cwd:
        print(f"In directory: {cwd}")
    
    if background:
        # Start process in background
        process = subprocess.Popen(
            command,
            shell=True,
            cwd=cwd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        return process
    else:
        # Run and wait for completion
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            capture_output=True,
            text=True
        )
        
        if result.stdout:
            print("Output:", result.stdout.strip())
        if result.stderr:
            print("Error:", result.stderr.strip())
        
        return result

def check_python_environment():
    """Check Python environment and dependencies."""
    print_step("Checking Python Environment")
    
    # Check Python version
    python_version = sys.version_info
    print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    if python_version.major < 3 or python_version.minor < 8:
        print("❌ Python 3.8+ required")
        return False
    
    # Check if we're in a virtual environment
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ Virtual environment detected")
    else:
        print("⚠️  Not in a virtual environment (recommended)")
    
    # Check required packages
    required_packages = [
        'fastapi',
        'uvicorn',
        'google-cloud-firestore',
        'google-cloud-pubsub',
        'requests',
        'python-dotenv'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - missing")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ Python environment ready")
    return True

def setup_environment_variables():
    """Setup and check environment variables."""
    print_step("Checking Environment Variables")
    
    # Load .env file if it exists
    env_file = ".env"
    if os.path.exists(env_file):
        print(f"✅ Found {env_file}")
        from dotenv import load_dotenv
        load_dotenv()
    else:
        print(f"⚠️  No {env_file} file found")
    
    # Check required environment variables
    required_vars = [
        "GCP_PROJECT_ID",
        "FIREBASE_CREDENTIALS_PATH",
        "PUBSUB_TOPIC_XERO_SYNC"
    ]
    
    missing_vars = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {value[:50]}{'...' if len(value) > 50 else ''}")
        else:
            print(f"❌ {var}: Not set")
            missing_vars.append(var)
    
    if missing_vars:
        print(f"\n❌ Missing environment variables: {', '.join(missing_vars)}")
        print("Please set these in your .env file or environment")
        return False
    
    print("✅ Environment variables ready")
    return True

def generate_firebase_token():
    """Generate a fresh Firebase token for testing."""
    print_step("Generating Firebase Token")
    
    token_file = "tests/firebase_id_token_ascii.txt"
    
    # Check if token file exists and is recent (less than 30 minutes old)
    if os.path.exists(token_file):
        file_age = time.time() - os.path.getmtime(token_file)
        if file_age < 1800:  # 30 minutes
            print(f"✅ Recent token file found (age: {int(file_age/60)} minutes)")
            return True
    
    print("Generating fresh Firebase token...")
    result = run_command("python tests/python/get_token.py")
    
    if result.returncode == 0:
        print("✅ Firebase token generated successfully")
        return True
    else:
        print("❌ Failed to generate Firebase token")
        print("Please check your Firebase credentials")
        return False

def start_backend_server():
    """Start the backend server."""
    print_step("Starting Backend Server")
    
    # Check if server is already running
    try:
        response = requests.get("http://localhost:8081/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is already running")
            return True
    except:
        pass
    
    print("Starting backend server...")
    
    # Start the server in background
    server_process = run_command(
        "python rest_api/run_server.py",
        background=True
    )
    
    # Wait for server to start
    print("Waiting for server to start...")
    for i in range(30):  # Wait up to 30 seconds
        try:
            response = requests.get("http://localhost:8081/health", timeout=2)
            if response.status_code == 200:
                print("✅ Backend server started successfully")
                return True
        except:
            pass
        
        time.sleep(1)
        print(f"  Waiting... ({i+1}/30)")
    
    print("❌ Backend server failed to start")
    return False

def check_frontend_environment():
    """Check frontend environment."""
    print_step("Checking Frontend Environment")
    
    frontend_dir = "../drcr_front"
    
    if not os.path.exists(frontend_dir):
        print("❌ Frontend directory not found")
        return False
    
    # Check if node_modules exists
    node_modules = os.path.join(frontend_dir, "node_modules")
    if not os.path.exists(node_modules):
        print("❌ Frontend dependencies not installed")
        print("Run: cd ../drcr_front && npm install")
        return False
    
    print("✅ Frontend environment ready")
    return True

def run_quick_health_check():
    """Run a quick health check of the system."""
    print_step("Running Health Check")
    
    # Test backend health endpoint
    try:
        response = requests.get("http://localhost:8081/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend health check passed")
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend health check failed: {e}")
        return False
    
    # Test authentication endpoint
    token_file = "tests/firebase_id_token_ascii.txt"
    if os.path.exists(token_file):
        try:
            with open(token_file, 'r') as f:
                token = f.read().strip()
            
            headers = {"Authorization": f"Bearer {token}"}
            response = requests.get("http://localhost:8081/auth/me", headers=headers, timeout=5)
            
            if response.status_code == 200:
                user_data = response.json()
                print(f"✅ Authentication check passed for: {user_data.get('email')}")
            else:
                print(f"❌ Authentication check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Authentication check failed: {e}")
            return False
    else:
        print("⚠️  No token file found, skipping auth check")
    
    print("✅ Health check completed")
    return True

def main():
    """Main setup function."""
    print_header("DRCR Automated Sync Test Environment Setup")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = True
    
    # Check Python environment
    if not check_python_environment():
        success = False
    
    # Setup environment variables
    if not setup_environment_variables():
        success = False
    
    # Generate Firebase token
    if not generate_firebase_token():
        success = False
    
    # Start backend server
    if not start_backend_server():
        success = False
    
    # Check frontend environment
    if not check_frontend_environment():
        success = False
    
    # Run health check
    if not run_quick_health_check():
        success = False
    
    if success:
        print_header("🎉 ENVIRONMENT SETUP COMPLETE!")
        print("""
✅ Environment is ready for testing!

Next steps:
1. Run the automated sync tests:
   python run_automated_sync_tests.py

2. Or run individual tests:
   python test_automated_sync_system.py

3. Test frontend components:
   cd ../drcr_front && npm test src/test/sync-status-test.tsx

4. Manual testing:
   • Open http://localhost:8081/docs for API documentation
   • Use the test scripts in tests/ directory
   • Check the dashboard for sync status display
""")
    else:
        print_header("❌ ENVIRONMENT SETUP FAILED")
        print("Please fix the issues above before running tests.")
        sys.exit(1)

if __name__ == "__main__":
    main()
