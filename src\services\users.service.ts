import { AuthService } from './auth.service';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8081';

export interface User {
  user_id: string;
  email: string;
  display_name?: string;
  role: 'firm_admin' | 'firm_staff';
  status: 'active' | 'inactive' | 'invited' | 'suspended';
  assigned_client_ids: string[];
  created_at?: string;
  updated_at?: string;
  last_login?: string;
  last_sign_in?: string;
  email_verified: boolean;
  document_id: string;
}

export interface UserDetails extends User {
  assigned_clients: Array<{
    client_id: string;
    name: string;
    status: string;
  }>;
  firebase_data: {
    email_verified: boolean;
    disabled: boolean;
    creation_time?: string;
    last_sign_in_time?: string;
    last_refresh_time?: string;
    provider_data?: Array<{
      provider_id: string;
      uid: string;
      email: string;
    }>;
  };
}

export interface UsersListResponse {
  users: User[];
  total_count: number;
  firm_id: string;
}

export interface UpdateUserRoleRequest {
  role: 'firm_admin' | 'firm_staff';
  assigned_client_ids: string[];
}

export interface UpdateUserStatusRequest {
  status: 'active' | 'inactive' | 'invited' | 'suspended';
}

export interface InviteUserRequest {
  email: string;
  role: 'firm_admin' | 'firm_staff';
  display_name?: string;
  assigned_client_ids?: string[];
}

export class UsersService {
  /**
   * Get authorization headers with Firebase token
   */
  private static async getAuthHeaders(): Promise<Record<string, string>> {
    const token = await AuthService.getCurrentUserToken();
    if (!token) {
      throw new Error('No authentication token available');
    }

    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * Handle API errors and convert them to user-friendly messages
   */
  private static handleApiError(error: any): Error {
    if (error.response?.data?.detail) {
      return new Error(error.response.data.detail);
    }
    if (error.message) {
      return new Error(error.message);
    }
    return new Error('An unexpected error occurred');
  }

  /**
   * List all users in the current firm
   */
  static async listUsers(): Promise<UsersListResponse> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/auth/users`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Get detailed information about a specific user
   */
  static async getUserDetails(userId: string): Promise<UserDetails> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/auth/users/${userId}`, {
        method: 'GET',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }

      return await response.json();
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Update a user's role and client assignments
   */
  static async updateUserRole(userId: string, data: UpdateUserRoleRequest): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/auth/users/${userId}/role`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Update a user's status (activate/deactivate)
   */
  static async updateUserStatus(userId: string, data: UpdateUserStatusRequest): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/auth/users/${userId}/status`, {
        method: 'PUT',
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Remove a user from the firm
   */
  static async removeUser(userId: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/auth/users/${userId}`, {
        method: 'DELETE',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Invite a new user to the firm
   */
  static async inviteUser(data: InviteUserRequest): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/auth/invite-user`, {
        method: 'POST',
        headers,
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }

  /**
   * Resend invitation email to a user with 'invited' status
   */
  static async resendInvite(userId: string): Promise<void> {
    try {
      const headers = await this.getAuthHeaders();
      const response = await fetch(`${API_BASE_URL}/auth/users/${userId}/resend-invite`, {
        method: 'POST',
        headers,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      throw this.handleApiError(error);
    }
  }
} 