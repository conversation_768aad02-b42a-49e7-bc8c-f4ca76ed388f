import { useState, useEffect } from 'react';
import { FirmService } from '../services/firm.service';
import { useAuthStore } from '../store/auth.store';

export interface UseFirmNameReturn {
  firmName: string;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook to manage firm name state
 * Automatically fetches and caches the firm name based on the current user's firmId
 */
export function useFirmName(): UseFirmNameReturn {
  const { user } = useAuthStore();
  const [firmName, setFirmName] = useState<string>('DRCR');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const fetchFirmName = async () => {
    if (!user?.firmId) {
      setFirmName('DRCR');
      setIsLoading(false);
      setError(null);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const name = await FirmService.getFirmNameCached(user.firmId);
      setFirmName(name);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch firm name';
      setError(errorMessage);
      console.error('Error fetching firm name:', err);
      
      // Set fallback name on error
      setFirmName(`Firm ${user.firmId.slice(-8)}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch firm name when user or firmId changes
  useEffect(() => {
    fetchFirmName();
  }, [user?.firmId]);

  // Refetch function for manual refresh
  const refetch = async () => {
    // Clear cache to force fresh fetch
    FirmService.clearCache();
    await fetchFirmName();
  };

  return {
    firmName,
    isLoading,
    error,
    refetch
  };
}
